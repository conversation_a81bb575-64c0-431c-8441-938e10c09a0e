<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue';

import { useVbenForm, useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { message, Upload, Button, Select, Tag, Input } from 'ant-design-vue';

import {
  createNews,
  updateNews,
  getAllCategories,
  getAllTags,
  uploadNewsImage,
  type NewsManagementApi,
} from '#/api/news-management';

import { useFormSchema } from '../data';

interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  success: [];
}>();

// 新闻ID，用于判断是新增还是编辑
const id = ref<number | undefined>();

const isUpdate = computed(() => !!id.value);

// 分类和标签选项
const categoryOptions = ref<Array<{ label: string; value: number }>>([]);
const tagOptions = ref<Array<{ label: string; value: string }>>([]);

// 表单数据
const formData = ref<Partial<NewsManagementApi.CreateNewsParams>>({});

// 富文本编辑器内容
const editorContent = ref('');

// 封面图片
const coverImageUrl = ref('');
const coverImageUploading = ref(false);

// 标签相关
const selectedTags = ref<string[]>([]);
const newTagName = ref('');

// 加载分类选项
async function loadCategories() {
  try {
    const res = await getAllCategories();
    if (res && res.data && Array.isArray(res.data)) {
      categoryOptions.value = res.data.map(cat => ({
        label: cat.name,
        value: cat.id,
      }));
    } else {
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    categoryOptions.value = [];
  }
}

// 加载标签选项
async function loadTags() {
  try {
    const res = await getAllTags();
    if (res && res.data && Array.isArray(res.data)) {
      tagOptions.value = res.data.map(tag => ({
        label: tag.name,
        value: tag.name,
      }));
    } else {
      tagOptions.value = [];
    }
  } catch (error) {
    console.error('加载标签失败:', error);
    tagOptions.value = [];
  }
}

// 表单配置
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入新闻标题',
        maxlength: 255,
        showCount: true,
      },
      fieldName: 'title',
      label: '新闻标题',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入新闻摘要',
        maxlength: 500,
        showCount: true,
        rows: 3,
      },
      fieldName: 'summary',
      label: '新闻摘要',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择新闻分类',
        options: categoryOptions,
      },
      fieldName: 'category_id',
      label: '新闻分类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入作者名称',
      },
      fieldName: 'author',
      label: '作者',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入来源链接',
        type: 'url',
      },
      fieldName: 'source_url',
      label: '来源链接',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '草稿', value: 'draft' },
          { label: '待审核', value: 'pending' },
          { label: '已发布', value: 'published' },
        ],
        placeholder: '请选择状态',
      },
      fieldName: 'status',
      label: '状态',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      fieldName: 'is_featured',
      label: '是否推荐',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择发布时间',
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'publish_date',
      label: '发布时间',
    },
  ],
  showDefaultActions: false,
});

// 封面图片上传
async function handleCoverUpload(file: File) {
  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    message.error('请选择图片文件（JPG、PNG、GIF、WebP）');
    return;
  }

  // 验证文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    message.error('图片大小不能超过5MB');
    return;
  }

  coverImageUploading.value = true;

  try {
    const res = await uploadNewsImage(file);

    // 检查响应数据
    if (res && res.data && res.data.url) {
      coverImageUrl.value = res.data.url;
      formApi.setFieldValue('cover_image_url', res.data.url);
      message.success('封面图片上传成功');
    } else {
      console.log('上传响应:', res);
      throw new Error('上传响应中没有返回图片URL');
    }
  } catch (error: any) {
    console.error('封面图片上传失败:', error);
    message.error(error.message || '图片上传失败，请重试');
  } finally {
    coverImageUploading.value = false;
  }
}

// 添加标签
function addTag(tagName?: string) {
  const tagToAdd = tagName || newTagName.value;
  if (tagToAdd && !selectedTags.value.includes(tagToAdd)) {
    selectedTags.value.push(tagToAdd);
    newTagName.value = '';
  }
}

// 移除标签
function removeTag(tag: string) {
  const index = selectedTags.value.indexOf(tag);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  }
}

// 创建Drawer组件
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请填写必填字段');
      return;
    }

    // 验证新闻内容
    if (!editorContent.value || editorContent.value.trim().length === 0) {
      message.error('新闻内容不能为空');
      return;
    }

    const values = await formApi.getValues();
    drawerApi.lock();

    const submitData: NewsManagementApi.CreateNewsParams = {
      ...values,
      content: editorContent.value,
      tags: selectedTags.value,
      cover_image_url: coverImageUrl.value,
    };

    try {
      if (id.value) {
        await updateNews(id.value, submitData);
      } else {
        await createNews(submitData);
      }

      emits('success');
      drawerApi.close();
      message.success(id.value ? '更新新闻成功' : '创建新闻成功');

      // 通知父组件刷新数据
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (error: any) {
      console.error('新闻保存失败:', error);
      message.error(error.message || '保存失败，请重试');
    } finally {
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<NewsManagementApi.News>();
      formApi.resetForm();

      if (data) {
        id.value = data.id;
        formApi.setValues({
          title: data.title,
          summary: data.summary,
          category_id: data.category?.id,
          author: data.author,
          source_url: data.sourceUrl,
          status: data.status,
          is_featured: data.isFeatured,
          publish_date: data.publishDate,
        });
        editorContent.value = data.content || '';
        coverImageUrl.value = data.coverImage || '';
        selectedTags.value = Array.isArray(data.tags) ? data.tags.map(tag => tag.name) : [];
      } else {
        id.value = undefined;
        editorContent.value = '';
        coverImageUrl.value = '';
        selectedTags.value = [];
      }
    }
  },
});

// 计算标题
const title = computed(() => {
  return id.value ? '编辑新闻' : '新增新闻';
});

onMounted(async () => {
  await loadCategories();
  await loadTags();

  // 动态更新分类选项
  if (formApi && formApi.schema && Array.isArray(formApi.schema)) {
    const categoryField = formApi.schema.find(item => item.fieldName === 'category_id');
    if (categoryField && categoryField.componentProps) {
      categoryField.componentProps.options = categoryOptions.value;
    }
  }
});

defineExpose({
  open: drawerApi.open,
  setData: drawerApi.setData,
});
</script>

<template>
  <Drawer
    :title="title"
    :width="800"
    class="news-form-drawer"
  >
    <Form />

    <!-- 封面图片上传 -->
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">封面图片</label>
      <div class="flex items-start gap-4">
        <Upload
          :show-upload-list="false"
          accept="image/*"
          :before-upload="(file) => { handleCoverUpload(file); return false; }"
        >
          <div
            v-if="!coverImageUrl"
            class="w-32 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-400"
          >
            <div class="text-center">
              <div class="text-gray-400 text-lg mb-1">+</div>
              <div class="text-xs text-gray-500">上传图片</div>
            </div>
          </div>
          <div
            v-else
            class="w-32 h-20 border border-gray-300 rounded-lg overflow-hidden cursor-pointer hover:opacity-80"
          >
            <img
              :src="coverImageUrl"
              alt="封面图片"
              class="w-full h-full object-cover"
            />
          </div>
        </Upload>

        <div v-if="coverImageUrl" class="flex-1">
          <div class="text-sm text-gray-600 mb-2">当前封面图片</div>
          <Button
            size="small"
            danger
            @click="() => { coverImageUrl = ''; formApi.setFieldValue('cover_image_url', ''); }"
          >
            删除图片
          </Button>
        </div>
      </div>
    </div>

    <!-- 富文本编辑器 -->
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">
        新闻内容 <span class="text-red-500">*</span>
      </label>
      <div class="border border-gray-300 rounded-lg">
        <!-- 这里应该集成富文本编辑器，暂时用textarea代替 -->
        <textarea
          v-model="editorContent"
          placeholder="请输入新闻内容..."
          class="w-full h-96 p-4 border-0 rounded-lg resize-none focus:outline-none"
        />
      </div>
      <div class="text-xs text-gray-500 mt-1">
        支持富文本格式，可插入图片、链接等
      </div>
    </div>

    <!-- 标签管理 -->
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">新闻标签</label>

      <!-- 已选标签 -->
      <div class="mb-3">
        <Tag
          v-for="tag in selectedTags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          class="mb-1"
        >
          {{ tag }}
        </Tag>
      </div>

      <!-- 添加标签 -->
      <div class="flex gap-2">
        <Select
          v-model:value="newTagName"
          placeholder="选择或输入新标签"
          :options="tagOptions"
          show-search
          allow-clear
          class="flex-1"
          @select="addTag"
        />
        <Button @click="addTag" :disabled="!newTagName">
          添加
        </Button>
      </div>
    </div>
  </Drawer>
</template>

<style scoped>
.news-form-drawer :deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
